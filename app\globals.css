@tailwind base;
@tailwind components;
@tailwind utilities;

@import "highlight.js/styles/github.min.css" (prefers-color-scheme: light);
@import "highlight.js/styles/github-dark.min.css" (prefers-color-scheme: dark);
@import "katex/dist/katex.min.css";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced dark mode styles */
.dark {
  color-scheme: dark;
}

/* Enhanced code block styling with copy functionality */
.prose .code-block-wrapper {
  @apply my-6 rounded-lg overflow-hidden border border-slate-200 dark:border-slate-700 relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.prose .code-block-header {
  @apply bg-slate-100 dark:bg-slate-800 px-4 py-2 text-sm font-mono text-slate-600 dark:text-slate-400 border-b border-slate-200 dark:border-slate-700 flex justify-between items-center;
}

.prose .language-label {
  @apply text-xs font-semibold uppercase tracking-wide text-slate-500 dark:text-slate-400;
}

.prose .copy-button {
  @apply opacity-0 transition-all duration-200 px-2 py-1 text-xs bg-slate-200 dark:bg-slate-700 hover:bg-slate-300 dark:hover:bg-slate-600 rounded font-sans flex items-center gap-1 cursor-pointer border-0;
}

.prose .code-block-wrapper:hover .copy-button {
  @apply opacity-100;
}

.prose .copy-button:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 ring-offset-slate-100 dark:ring-offset-slate-800 opacity-100;
}

.prose pre[class*="language-"] {
  @apply bg-slate-50 dark:bg-slate-900 border-0 rounded-none m-0;
  padding: 1rem;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.5;
}

.prose code[class*="language-"] {
  font-family: "Fira Code", "Monaco", "Cascadia Code", "Roboto Mono", monospace;
  font-size: 0.875rem;
}

/* Light mode Prism colors */
.prose pre[class*="language-"] .token.comment,
.prose pre[class*="language-"] .token.prolog,
.prose pre[class*="language-"] .token.doctype,
.prose pre[class*="language-"] .token.cdata {
  color: #6b7280;
  font-style: italic;
}

.prose pre[class*="language-"] .token.punctuation {
  color: #374151;
}

.prose pre[class*="language-"] .token.property,
.prose pre[class*="language-"] .token.tag,
.prose pre[class*="language-"] .token.boolean,
.prose pre[class*="language-"] .token.number,
.prose pre[class*="language-"] .token.constant,
.prose pre[class*="language-"] .token.symbol,
.prose pre[class*="language-"] .token.deleted {
  color: #dc2626;
}

.prose pre[class*="language-"] .token.selector,
.prose pre[class*="language-"] .token.attr-name,
.prose pre[class*="language-"] .token.string,
.prose pre[class*="language-"] .token.char,
.prose pre[class*="language-"] .token.builtin,
.prose pre[class*="language-"] .token.inserted {
  color: #059669;
}

.prose pre[class*="language-"] .token.operator,
.prose pre[class*="language-"] .token.entity,
.prose pre[class*="language-"] .token.url,
.prose pre[class*="language-"] .language-css .token.string,
.prose pre[class*="language-"] .style .token.string {
  color: #0891b2;
}

.prose pre[class*="language-"] .token.atrule,
.prose pre[class*="language-"] .token.attr-value,
.prose pre[class*="language-"] .token.keyword {
  color: #7c3aed;
  font-weight: 600;
}

.prose pre[class*="language-"] .token.function,
.prose pre[class*="language-"] .token.class-name {
  color: #dc2626;
  font-weight: 500;
}

.prose pre[class*="language-"] .token.regex,
.prose pre[class*="language-"] .token.important,
.prose pre[class*="language-"] .token.variable {
  color: #ea580c;
}

/* Dark mode Prism colors */
.dark .prose pre[class*="language-"] {
  @apply bg-slate-900 border-slate-700;
}

.dark .prose pre[class*="language-"] .token.comment,
.dark .prose pre[class*="language-"] .token.prolog,
.dark .prose pre[class*="language-"] .token.doctype,
.dark .prose pre[class*="language-"] .token.cdata {
  color: #9ca3af;
  font-style: italic;
}

.dark .prose pre[class*="language-"] .token.punctuation {
  color: #d1d5db;
}

.dark .prose pre[class*="language-"] .token.property,
.dark .prose pre[class*="language-"] .token.tag,
.dark .prose pre[class*="language-"] .token.boolean,
.dark .prose pre[class*="language-"] .token.number,
.dark .prose pre[class*="language-"] .token.constant,
.dark .prose pre[class*="language-"] .token.symbol,
.dark .prose pre[class*="language-"] .token.deleted {
  color: #f87171;
}

.dark .prose pre[class*="language-"] .token.selector,
.dark .prose pre[class*="language-"] .token.attr-name,
.dark .prose pre[class*="language-"] .token.string,
.dark .prose pre[class*="language-"] .token.char,
.dark .prose pre[class*="language-"] .token.builtin,
.dark .prose pre[class*="language-"] .token.inserted {
  color: #34d399;
}

.dark .prose pre[class*="language-"] .token.operator,
.dark .prose pre[class*="language-"] .token.entity,
.dark .prose pre[class*="language-"] .token.url,
.dark .prose pre[class*="language-"] .language-css .token.string,
.dark .prose pre[class*="language-"] .style .token.string {
  color: #22d3ee;
}

.dark .prose pre[class*="language-"] .token.atrule,
.dark .prose pre[class*="language-"] .token.attr-value,
.dark .prose pre[class*="language-"] .token.keyword {
  color: #a78bfa;
  font-weight: 600;
}

.dark .prose pre[class*="language-"] .token.function,
.dark .prose pre[class*="language-"] .token.class-name {
  color: #f87171;
  font-weight: 500;
}

.dark .prose pre[class*="language-"] .token.regex,
.dark .prose pre[class*="language-"] .token.important,
.dark .prose pre[class*="language-"] .token.variable {
  color: #fb923c;
}

/* Enhanced inline code styling */
.prose :not(pre) > code {
  @apply bg-slate-100 dark:bg-slate-800 text-slate-900 dark:text-slate-100 px-1.5 py-0.5 rounded text-sm font-mono border border-slate-200 dark:border-slate-700;
}

/* Better prose styling for dark mode */
.dark .prose {
  @apply text-slate-100;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3,
.dark .prose h4,
.dark .prose h5,
.dark .prose h6 {
  @apply text-slate-100;
}

.dark .prose strong {
  @apply text-slate-100;
}

.dark .prose a {
  @apply text-blue-400 hover:text-blue-300;
}

.dark .prose blockquote {
  @apply text-slate-300 border-slate-600 bg-slate-800/50 rounded-lg p-4;
}

.dark .prose hr {
  @apply border-slate-700;
}

.dark .prose table {
  @apply border-slate-700;
}

.dark .prose th,
.dark .prose td {
  @apply border-slate-700;
}

.dark .prose th {
  @apply bg-slate-800;
}

/* Enhanced list styling */
.prose ul,
.prose ol {
  @apply space-y-2;
}

.prose li {
  @apply text-slate-700 dark:text-slate-300;
}

/* Enhanced callout styling */
.prose .callout {
  @apply my-4 p-4 rounded-lg border-l-4;
}

.prose .callout-note {
  @apply bg-blue-50 dark:bg-blue-950/30 border-blue-400 dark:border-blue-500;
}

.prose .callout-tip {
  @apply bg-green-50 dark:bg-green-950/30 border-green-400 dark:border-green-500;
}

.prose .callout-important {
  @apply bg-purple-50 dark:bg-purple-950/30 border-purple-400 dark:border-purple-500;
}

.prose .callout-warning {
  @apply bg-yellow-50 dark:bg-yellow-950/30 border-yellow-400 dark:border-yellow-500;
}

.prose .callout-caution {
  @apply bg-red-50 dark:bg-red-950/30 border-red-400 dark:border-red-500;
}

.prose .callout-title {
  @apply font-semibold flex items-center gap-2 mb-2;
}

.prose .callout-note .callout-title {
  @apply text-blue-800 dark:text-blue-200;
}

.prose .callout-tip .callout-title {
  @apply text-green-800 dark:text-green-200;
}

.prose .callout-important .callout-title {
  @apply text-purple-800 dark:text-purple-200;
}

.prose .callout-warning .callout-title {
  @apply text-yellow-800 dark:text-yellow-200;
}

.prose .callout-caution .callout-title {
  @apply text-red-800 dark:text-red-200;
}

.prose .callout-content {
  @apply text-slate-700 dark:text-slate-300;
}

/* Enhanced table styling */
.prose .table-wrapper {
  @apply overflow-x-auto my-6 rounded-lg border border-slate-200 dark:border-slate-700;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.prose table {
  @apply min-w-full border-collapse;
}

.prose th {
  @apply bg-slate-100 dark:bg-slate-800 px-4 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100 border-b border-slate-200 dark:border-slate-700;
}

.prose td {
  @apply px-4 py-3 text-sm text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700;
}

.prose tbody tr:last-child td {
  @apply border-b-0;
}

.prose tbody tr:hover {
  @apply bg-slate-50 dark:bg-slate-800/50;
}

/* Enhanced blockquote styling */
.prose blockquote {
  @apply border-l-4 border-slate-300 dark:border-slate-600 pl-4 py-2 my-4 bg-slate-50 dark:bg-slate-800/50 rounded-r-lg italic;
}

.prose blockquote p {
  @apply text-slate-700 dark:text-slate-300 mb-0;
}

/* Enhanced list styling */
.prose ul {
  @apply space-y-2;
}

.prose ol {
  @apply space-y-2;
}

.prose li {
  @apply text-slate-700 dark:text-slate-300;
}

.prose li::marker {
  @apply text-slate-500 dark:text-slate-400;
}

/* Enhanced heading styling */
.prose h1 {
  @apply text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4 mt-6 pb-2 border-b border-slate-200 dark:border-slate-700;
}

.prose h2 {
  @apply text-xl font-bold text-slate-900 dark:text-slate-100 mb-3 mt-5 pb-1 border-b border-slate-200 dark:border-slate-700;
}

.prose h3 {
  @apply text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2 mt-4;
}

.prose h4 {
  @apply text-base font-semibold text-slate-900 dark:text-slate-100 mb-2 mt-3;
}

.prose h5 {
  @apply text-sm font-semibold text-slate-900 dark:text-slate-100 mb-1 mt-3;
}

.prose h6 {
  @apply text-sm font-medium text-slate-700 dark:text-slate-300 mb-1 mt-2;
}

/* Enhanced link styling */
.prose a {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline decoration-blue-300 dark:decoration-blue-600 underline-offset-2 transition-colors;
}

/* Enhanced horizontal rule */
.prose hr {
  @apply border-slate-300 dark:border-slate-600 my-6;
}

/* Enhanced emphasis */
.prose strong {
  @apply font-semibold text-slate-900 dark:text-slate-100;
}

.prose em {
  @apply italic text-slate-800 dark:text-slate-200;
}

/* Enhanced paragraph spacing */
.prose p {
  @apply mb-4 text-slate-700 dark:text-slate-300 leading-relaxed;
}

.prose p:last-child {
  @apply mb-0;
}

/* Enhanced math equation styling */
.prose .math-block {
  @apply my-6 p-4 bg-slate-50 dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-lg overflow-x-auto;
}

.prose .math-block .katex-display {
  @apply m-0;
}

.prose .katex {
  font-size: 1.1em;
}

.prose .math-error {
  @apply my-4 p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-300 font-mono text-sm;
}

/* Enhanced code block with language label and copy button */
.prose .enhanced-markdown .code-block-wrapper {
  @apply relative;
}

.prose .enhanced-markdown .code-block-wrapper:hover .copy-button {
  @apply opacity-100;
}

.prose .enhanced-markdown .language-label {
  @apply text-xs font-semibold uppercase tracking-wide;
}

.prose .enhanced-markdown .copy-button:hover {
  @apply bg-slate-300 dark:bg-slate-600;
}

/* Smooth transitions for interactive elements */
.prose .enhanced-markdown * {
  transition: all 0.2s ease-in-out;
}

/* Enhanced focus states for accessibility */
.prose .enhanced-markdown .copy-button:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 ring-offset-slate-100 dark:ring-offset-slate-800;
}

/* Better spacing for nested elements */
.prose .enhanced-markdown > *:first-child {
  @apply mt-0;
}

.prose .enhanced-markdown > *:last-child {
  @apply mb-0;
}

/* Enhanced readability improvements */
.prose .enhanced-markdown {
  @apply leading-relaxed;
}

.prose .enhanced-markdown p {
  @apply mb-4 last:mb-0;
}

.prose .enhanced-markdown ul,
.prose .enhanced-markdown ol {
  @apply mb-4;
}

.prose .enhanced-markdown li {
  @apply mb-1;
}

/* Better contrast for dark mode */
.dark .prose .enhanced-markdown {
  @apply text-slate-200;
}

.dark .prose .enhanced-markdown h1,
.dark .prose .enhanced-markdown h2,
.dark .prose .enhanced-markdown h3,
.dark .prose .enhanced-markdown h4,
.dark .prose .enhanced-markdown h5,
.dark .prose .enhanced-markdown h6 {
  @apply text-slate-100;
}

.prose pre[class*="language-"]:hover::after {
  content: "Copy";
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  opacity: 0.8;
  font-family: system-ui, -apple-system, sans-serif;
}

.dark .prose pre[class*="language-"]:hover::after {
  background: rgba(255, 255, 255, 0.2);
}
